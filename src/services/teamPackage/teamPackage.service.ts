'use strict';
import { getModelForClass, mongoose } from '@typegoose/typegoose';
import { Action, Event, Service as DService } from 'moleculer-decorators';
import * as MoleculerTs from 'moleculer-ts';
import { RequireRoles } from '../../commons/annotation.helper';
import { CommonConfig } from '../../commons/common.config';
import { CommonErrors } from '../../commons/error.helper';
import { STATUSES } from '../../entities/base.entity';
import { ITeamPackage } from '../../entities/teamPackage.entity';
import { USER_ROLE } from '../../entities/user.entity';
import { DbBaseMixin, DbBaseMixinActionTypes } from '../../mixins';
import ConfigMixin from '../../mixins/config.mixin';
import { TeamPackage, TeamPackageType } from '../../models/teamPackage';
import { ApiGatewayMeta, MoleculerDBService, TeamPackageServiceTypes } from '../../types';
import { Context, eventName } from '../../types/moleculer';
import { convertPaginateOptions, convertPaginateQuery } from '../../utils/mongoPlugins/paginate';
import { OwnActions } from './teamPackage.service.types';

const dbBaseMixin = new DbBaseMixin({
  dbUri: CommonConfig.DB_URI,
  name: 'dbTeamPackageMixin',
  collection: 'teamPackage',
  model: getModelForClass(TeamPackage),
});

@DService({
  name: 'teamPackage',
  mixins: [ConfigMixin(['teamPackage.**']), dbBaseMixin.getMixin()],
  settings: {
    fields: [
      '_id',
      'teamId',
      'packageId',
      'packageName',
      'startDate',
      'expirationDate',
      'status',
      'knowledgeLimit',
      'memberLimit',
      'creditLimit',
      'monthlyCreditLimit',
      'botSettingLimit',
      'connectorLimit',
      'ccuLimit',
      'price',
      'currentUsage',
    ],
  },
})
class TeamPackageService
  extends MoleculerDBService<
    {
      fields: string[];
    },
    TeamPackageType
  >
  implements Omit<MoleculerTs.GetServiceOwnActions<OwnActions>, DbBaseMixinActionTypes>
{
  @RequireRoles([USER_ROLE.ADMIN, USER_ROLE.MODERATOR])
  @Action({
    rest: 'POST /assign',
    params: {
      teamId: 'string',
      packageId: 'string',
    },
  })
  async actionAssign(
    ctx: Context<TeamPackageServiceTypes.ActionParams<'actionAssign'>>,
  ): Promise<TeamPackageServiceTypes.ActionReturn<'actionAssign'>> {
    const { teamId, packageId } = ctx.params;

    // Get package details
    const packageDetails = await ctx.call('package.actionGet', { id: packageId });
    if (!packageDetails) {
      throw new CommonErrors.NotFoundError('Package not found');
    }

    // Calculate expiration date based on package days
    const startDate = new Date();
    const expirationDate = new Date();
    expirationDate.setDate(startDate.getDate() + (packageDetails.days || 0));

    // Update existing package or create new one
    const teamPackage = await this.adapter.model.findOneAndUpdate(
      {
        teamId: new mongoose.Types.ObjectId(teamId),
        status: STATUSES.ACTIVE,
      },
      {
        $set: {
          packageId: new mongoose.Types.ObjectId(packageId),
          packageName: packageDetails.name || 'Unknown Package',
          startDate,
          expirationDate,
          status: STATUSES.ACTIVE,
          // Copy package limits
          knowledgeLimit: packageDetails.knowledgeLimit || 0,
          memberLimit: packageDetails.memberLimit || 0,
          knowledgeRecordLimit: packageDetails.knowledgeRecordLimit || 0,
          creditLimit: packageDetails.creditLimit || 0,
          monthlyCreditLimit: packageDetails.monthlyCreditLimit || 0,
          botSettingLimit: packageDetails.botSettingLimit || 0,
          connectorLimit: packageDetails.connectorLimit || 0,
          ccuLimit: packageDetails.ccuLimit || 0,
          price: packageDetails.price || 0,
          currentUsage: {
            monthlyCreditsUsed: 0,
          },
        },
      },
      {
        upsert: true,
        new: true,
        setDefaultsOnInsert: true,
      },
    );

    const doc = (await this.transformDocuments(ctx, {}, teamPackage)) as ITeamPackage;

    // fix object id unable to convert to string
    doc.teamId = doc.teamId.toString();
    doc.packageId = doc.packageId.toString();

    this.broker.broadcast('teamPackage.assigned', { teamPackage: doc, package: packageDetails });
    return doc;
  }

  @RequireRoles([USER_ROLE.ADMIN, USER_ROLE.MODERATOR])
  @Action({
    rest: 'PUT /extend',
    params: {
      id: 'string',
    },
  })
  async actionExtend(
    ctx: Context<TeamPackageServiceTypes.ActionParams<'actionExtend'>>,
  ): Promise<TeamPackageServiceTypes.ActionReturn<'actionExtend'>> {
    const { id } = ctx.params;

    // Get current team package
    const teamPackage = await this.adapter.model.findById(id);
    if (!teamPackage) {
      throw new CommonErrors.NotFoundError('Team package not found');
    }

    // Get package details to get days
    const packageDetails = await ctx.call('package.actionGet', { id: teamPackage.packageId?.toString() });
    if (!packageDetails) {
      throw new CommonErrors.NotFoundError('Package not found');
    }

    // Calculate new expiration date
    const newExpirationDate = new Date(teamPackage.expirationDate);
    newExpirationDate.setDate(newExpirationDate.getDate() + (packageDetails.days || 0));

    // Prepare update with extended limits
    const updateData = {
      $set: { expirationDate: newExpirationDate },
      $inc: {
        creditLimit: packageDetails.creditLimit || 0,
      },
    };

    const updatedPackage = await this.adapter.model
      .findByIdAndUpdate(id, updateData, { new: true })
      .catch((_): undefined => undefined);

    if (!updatedPackage) {
      throw new CommonErrors.NotFoundError('Team package not found');
    }

    const doc = (await this.transformDocuments(ctx, {}, updatedPackage)) as ITeamPackage;

    this.broker.broadcast('teamPackage.extended', doc);
    return doc;
  }

  @Action({
    // rest: 'PUT /updateUsage',
    params: {
      id: 'string',
      usage: 'object',
    },
  })
  async actionUpdateUsage(
    ctx: Context<TeamPackageServiceTypes.ActionParams<'actionUpdateUsage'>>,
  ): Promise<TeamPackageServiceTypes.ActionReturn<'actionUpdateUsage'>> {
    const { id, usage } = ctx.params;

    const teamPackage = await this.adapter.model.findByIdAndUpdate(
      id,
      { $set: { currentUsage: usage } },
      { new: true },
    );

    if (!teamPackage) {
      throw new CommonErrors.NotFoundError('Team package not found');
    }

    const doc = (await this.transformDocuments(ctx, {}, teamPackage)) as ITeamPackage;
    this.broker.broadcast('teamPackage.usageUpdated', doc);
    return doc;
  }

  @RequireRoles([USER_ROLE.ADMIN, USER_ROLE.MODERATOR])
  @Action({
    rest: 'POST /list',
    params: {
      page: { type: 'number', integer: true, min: 1, optional: true, convert: true },
      pageSize: { type: 'number', integer: true, min: 0, optional: true, convert: true },
      sort: { type: 'string', optional: true },
      search: { type: 'string', optional: true },
    },
  })
  async actionList(
    ctx: Context<TeamPackageServiceTypes.ActionParams<'actionList'>>,
  ): Promise<TeamPackageServiceTypes.ActionReturn<'actionList'>> {
    return this.adapter.model.paginate(
      convertPaginateQuery({ searchFields: ['packageName'], ...ctx.params }),
      convertPaginateOptions(
        { ...ctx.params, fields: this.settings.fields },
        this.adapter.model.defaultPaginateOptions,
      ),
    );
  }

  @Action({
    // rest: 'POST /addCreditUsage',
    params: {
      teamId: 'string',
      credits: 'number',
    },
  })
  async actionAddCreditUsage(
    ctx: Context<TeamPackageServiceTypes.ActionParams<'actionAddCreditUsage'>>,
  ): Promise<TeamPackageServiceTypes.ActionReturn<'actionAddCreditUsage'>> {
    const { teamId, credits } = ctx.params;

    const currentDate = new Date();
    const currentMonth = currentDate.getMonth() + 1;

    // Get current team package to check month
    const existingPackage = await this.adapter.model.findOne({
      teamId: new mongoose.Types.ObjectId(teamId),
      status: STATUSES.ACTIVE,
    });

    // If month has changed, reset monthly credits
    const resetMonthlyCredits = existingPackage?.currentUsage?.monthDigit !== currentMonth;

    const updateQuery: any = {
      $inc: {
        'currentUsage.creditsUsed': credits,
      },
      $set: {
        'currentUsage.monthDigit': currentMonth,
      },
    };

    if (resetMonthlyCredits) {
      updateQuery.$set['currentUsage.monthlyCreditsUsed'] = credits;
    } else {
      updateQuery.$inc['currentUsage.monthlyCreditsUsed'] = credits;
    }

    const teamPackage = await this.adapter.model.findOneAndUpdate(
      {
        teamId: new mongoose.Types.ObjectId(teamId),
        status: STATUSES.ACTIVE,
      },
      updateQuery,
      {
        new: true,
      },
    );

    if (!teamPackage) {
      throw new CommonErrors.NotFoundError('No active package found for team');
    }

    return (await this.transformDocuments(ctx, {}, teamPackage)) as ITeamPackage;
  }

  @Action({
    // rest: 'POST /updateTeamPackagesByPackageId',
    params: {
      packageId: 'string',
      updates: 'object',
    },
  })
  async actionUpdateTeamPackagesByPackageId(
    ctx: Context<TeamPackageServiceTypes.ActionParams<'actionUpdateTeamPackagesByPackageId'>>,
  ): Promise<TeamPackageServiceTypes.ActionReturn<'actionUpdateTeamPackagesByPackageId'>> {
    const { packageId, updates } = ctx.params;

    // Get all active team packages for this package ID
    const teamPackages = await this.adapter.model.find({
      packageId: new mongoose.Types.ObjectId(packageId),
      status: STATUSES.ACTIVE,
    });

    // Update each team package with the new values
    const updatedPackages = await Promise.all(
      teamPackages.map(async (teamPackage) => {
        // Only update the fields that exist in both updates and team package
        const updatedFields = {
          packageName: updates.name ?? teamPackage.packageName,
          knowledgeLimit: updates.knowledgeLimit ?? teamPackage.knowledgeLimit,
          knowledgeRecordLimit: updates.knowledgeRecordLimit ?? teamPackage.knowledgeRecordLimit,
          memberLimit: updates.memberLimit ?? teamPackage.memberLimit,
          creditLimit: updates.creditLimit ?? teamPackage.creditLimit,
          monthlyCreditLimit: updates.monthlyCreditLimit ?? teamPackage.monthlyCreditLimit,
          botSettingLimit: updates.botSettingLimit ?? teamPackage.botSettingLimit,
          connectorLimit: updates.connectorLimit ?? teamPackage.connectorLimit,
          ccuLimit: updates.ccuLimit ?? teamPackage.ccuLimit,
          price: updates.price ?? teamPackage.price,
        };

        return this.adapter.model.findByIdAndUpdate(teamPackage._id, { $set: updatedFields }, { new: true });
      }),
    );

    return updatedPackages.filter(Boolean);
  }

  @Action({
    // rest: 'POST /checkCreditLimits',
    params: {
      teamId: 'string',
    },
  })
  async actionCheckCreditLimits(
    ctx: Context<TeamPackageServiceTypes.ActionParams<'actionCheckCreditLimits'>>,
  ): Promise<TeamPackageServiceTypes.ActionReturn<'actionCheckCreditLimits'>> {
    const { teamId } = ctx.params;

    const teamPackage = await this.adapter.model.findOne({
      teamId: new mongoose.Types.ObjectId(teamId),
      status: STATUSES.ACTIVE,
    });

    if (!teamPackage) {
      return {
        isValid: false,
        errors: ['No active package found for team'],
      };
    }

    const errors: string[] = [];

    // Check if package is expired
    if (teamPackage.expirationDate && new Date(teamPackage.expirationDate) < new Date()) {
      errors.push('Team package has expired');
    }

    // Check monthly credit limit
    if (
      teamPackage.monthlyCreditLimit &&
      teamPackage.currentUsage?.monthlyCreditsUsed &&
      teamPackage.currentUsage.monthlyCreditsUsed >= teamPackage.monthlyCreditLimit
    ) {
      errors.push('Monthly credit limit exceeded');
    }

    // Check total credit limit
    if (
      teamPackage.creditLimit &&
      teamPackage.currentUsage?.creditsUsed &&
      teamPackage.currentUsage.creditsUsed >= teamPackage.creditLimit
    ) {
      errors.push('Total credit limit exceeded');
    }

    return {
      isValid: errors.length === 0,
      errors,
      package: teamPackage,
    };
  }

  @RequireRoles()
  @Action({
    rest: 'GET /current',
  })
  async actionGetCurrent(
    ctx: Context<TeamPackageServiceTypes.ActionParams<'actionGetCurrent'>, ApiGatewayMeta>,
  ): Promise<TeamPackageServiceTypes.ActionReturn<'actionGetCurrent'>> {
    const teamId = ctx.meta.user.teamId;

    const teamPackage = await this.adapter.model
      .find({
        teamId: new mongoose.Types.ObjectId(teamId),
        status: STATUSES.ACTIVE,
      })
      .lean({ parseId: true });

    if (!teamPackage) {
      throw new CommonErrors.NotFoundError('No active package found for team');
    }

    console.log('teamPackage', teamPackage);
    // @ts-ignore
    return teamPackage;
  }

  @Event({
    name: eventName('teamPackage.assigned'),
  })
  async onAssigned(payload: ITeamPackage) {
    this.logger.info(`Package assigned to team: ${payload.teamId}`);
  }

  @Event({
    name: eventName('teamPackage.extended'),
  })
  async onExtended(payload: ITeamPackage) {
    this.logger.info(`Package extended for team: ${payload.teamId}`);
  }

  @Event({
    name: eventName('teamPackage.usageUpdated'),
  })
  async onUsageUpdated(payload: ITeamPackage) {
    this.logger.info(`Usage updated for team package: ${payload._id}`);
  }
}

export = TeamPackageService;
